using UnityEngine;
using UnityEngine.InputSystem;

public class IPS03InputAction : MonoBehaviour
{
    public InputActionAsset inputActionAsset;
    private InputAction moveAction;
    private Vector2 moveVector;
    public float rotationSpeed = 10f;

    void Start()
    {
        if (inputActionAsset == null)
        {
            Debug.LogError("InputActionAsset is null");
            return;
        }

        moveAction = inputActionAsset.FindAction("Player/Move");
        moveAction.started += OnMoveStarted;
        moveAction.performed += OnMovePerformed;
        moveAction.canceled += OnMoveCanceled;
    }

    void Update()
    {
        if (moveVector != Vector2.zero)
        {
            // 每帧移动
            transform.Translate(new Vector3(moveVector.x, 0, moveVector.y) * 3 * Time.deltaTime);
        }

        if (moveVector != Vector2.zero)
        {
            // 计算目标旋转
            Quaternion targetRotation = Quaternion.LookRotation(new Vector3(moveVector.x, 0, moveVector.y).normalized);

            // 以每秒rotationSpeed度数的速度旋转物体
            transform.rotation = Quaternion.Slerp(
                transform.rotation,
                targetRotation,
                rotationSpeed * Time.deltaTime);
        }
    }

    // 行为一开始让角色看向移动方向
    private void OnMoveStarted(InputAction.CallbackContext context)
    {
        Debug.Log("Move Started");
    }

    // 让角色持续移动
    private void OnMovePerformed(InputAction.CallbackContext context)
    {
        moveVector = context.ReadValue<Vector2>();
    }

    // 让角色停止移动
    private void OnMoveCanceled(InputAction.CallbackContext context)
    {
        moveVector = Vector2.zero;
    }
}
