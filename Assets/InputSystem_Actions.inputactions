{"version": 1, "name": "InputSystem_Actions", "maps": [{"name": "Player", "id": "a8d297d3-9d55-4c8e-a8bd-74bc75862222", "actions": [{"name": "Move", "type": "Value", "id": "52a28d12-1c19-4afb-8513-b77e42de280b", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Look", "type": "Value", "id": "139ab3e1-fd81-4b92-ac32-a0f093cc2524", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Attack", "type": "<PERSON><PERSON>", "id": "d5cf4dc3-9335-4145-8421-1a917c5e8cfc", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Interact", "type": "<PERSON><PERSON>", "id": "650a6b47-4138-43fe-bfc2-779dc0d19d20", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "Hold", "initialStateCheck": false}, {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON>", "id": "a6905885-7810-4426-8095-4a3fd0efab82", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Jump", "type": "<PERSON><PERSON>", "id": "670cce7f-36fd-4cb0-b085-cef6f47852c6", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Previous", "type": "<PERSON><PERSON>", "id": "473bb436-e216-453e-b98e-24f949694812", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Next", "type": "<PERSON><PERSON>", "id": "c67d936b-b19e-4d76-a345-8adbe42ace07", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Sprint", "type": "<PERSON><PERSON>", "id": "b07442bd-389f-4f01-96d4-4b798a850d83", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "New action", "type": "Value", "id": "b5f09f40-29c2-4934-a2ac-be34b573adf5", "expectedControlType": "", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "", "id": "93675f6f-d3e1-4a3d-809a-c2cd66a381d6", "path": "<Gamepad>/leftStick", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "WASD", "id": "08780ffa-eb43-4d23-8431-2b0cb66fda55", "path": "Dpad", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "dea8b7bd-9603-45b0-ac56-20106ff18732", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "df42e98c-17a6-4b98-a928-6472643f02b3", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "6c308549-e672-4dfa-aeb8-b74b01a4d596", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "68091858-f3f9-4551-8ff1-e9a8f9997f4f", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "00c2554b-65c4-4f31-9875-953544d0907c", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "1116bcdc-8ad2-4369-a82f-155b39c6876f", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "9a0fe63b-7936-4def-a3a1-b86154363c19", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "67100f89-b9db-43b7-b92b-5db258b685a7", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Move", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "b49b691b-ec2d-48f7-8247-357f0d944a53", "path": "<XRController>/{Primary2DAxis}", "interactions": "", "processors": "", "groups": "XR", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "32eb06f0-ff95-4c1e-b4ad-0754aab01931", "path": "<Joystick>/stick", "interactions": "", "processors": "", "groups": "Joystick", "action": "Move", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "f6cac4f8-fc93-4ba7-8475-ae63ad341364", "path": "<Gamepad>/rightStick", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "342572df-9c7f-4442-8cef-820e596b2c24", "path": "<Pointer>/delta", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse;Touch", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "31aeb4f8-0b0d-419d-8c00-2d0c867866cb", "path": "<Joystick>/{Hatswitch}", "interactions": "", "processors": "", "groups": "Joystick", "action": "Look", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b074dc0b-2d91-4d47-9c32-32f2dcbbb9ed", "path": "<Gamepad>/buttonWest", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2e7225de-b0f7-4290-a314-402255e9b617", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d388be49-2be2-4459-9388-c4c51e5546e4", "path": "<Touchscreen>/primaryTouch/tap", "interactions": "", "processors": "", "groups": ";Touch", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "dce77474-f082-4619-ad2f-51bbc5fcad00", "path": "<Joystick>/trigger", "interactions": "", "processors": "", "groups": "Joystick", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "296b2b8f-2358-45fd-9a77-3c756a8ee8db", "path": "<XRController>/{PrimaryAction}", "interactions": "", "processors": "", "groups": "XR", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7be25068-2c81-4e4b-a19c-76e1e25148b5", "path": "<Keyboard>/enter", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Attack", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "477d3152-d900-4516-91d2-6003900b7020", "path": "<Keyboard>/2", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Next", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "80f0e23e-37f2-4af2-829c-ea6e9132819d", "path": "<Gamepad>/dpad/right", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Next", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "81bcb8ac-0bf4-4366-b70a-b7502204efb7", "path": "<Keyboard>/leftShift", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "041b31f5-3b46-420b-a4bb-faf97dd49a8c", "path": "<Gamepad>/leftStickPress", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "83b23a70-9fd2-4cdb-b712-7cdaf509d55f", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Sprint", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "7b2c1d1c-078d-431d-a6b6-9958ae2fe976", "path": "<Keyboard>/space", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "8f0feec8-8583-4949-90ff-e4ee555e3d88", "path": "<Gamepad>/buttonSouth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "17b0f99f-13fc-4dd2-b6a1-5736ab34e328", "path": "<XRController>/secondaryButton", "interactions": "", "processors": "", "groups": "XR", "action": "Jump", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "051be4c2-68a7-4d07-aff4-819e3a9cc293", "path": "<Keyboard>/1", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Previous", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "082b65a7-ee80-41fd-9bc3-f1655cdc4b8f", "path": "<Gamepad>/dpad/left", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Previous", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "67dadd35-ac87-46ed-a496-7aa500587435", "path": "<Keyboard>/e", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "4bac9d1c-a084-493c-acf7-a23fec067b56", "path": "<Gamepad>/buttonNorth", "interactions": "", "processors": "", "groups": "Gamepad", "action": "Interact", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "48a3183e-3c21-4c79-a967-2414d94879b5", "path": "<Gamepad>/buttonEast", "interactions": "", "processors": "", "groups": "Gamepad", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "d0c6d519-2ab9-453d-b2d2-9af2b82f1da7", "path": "<Keyboard>/c", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "<PERSON><PERSON>", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6e850d81-8555-4b37-a938-2b4a0d57efd0", "path": "*/{Back}", "interactions": "", "processors": "", "groups": "", "action": "New action", "isComposite": false, "isPartOfComposite": false}]}, {"name": "UI", "id": "ea94d11c-8c29-4347-b43c-9c8100488f0c", "actions": [{"name": "Navigate", "type": "PassThrough", "id": "2cde1dbb-e1f2-48a4-a285-a25d370eddfb", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Submit", "type": "<PERSON><PERSON>", "id": "9d679736-ac42-4414-89c6-1de02a668a6e", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Cancel", "type": "<PERSON><PERSON>", "id": "f4d43121-94db-4200-be47-e6b7d584356b", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "Point", "type": "PassThrough", "id": "5238398b-3369-44a9-9c29-908c776759c6", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "Click", "type": "PassThrough", "id": "b175a52b-f690-4050-bbe7-12307c832d9d", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": true}, {"name": "RightClick", "type": "PassThrough", "id": "0144127a-1bb0-4dfa-ac90-7c7e84ca94ed", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "MiddleClick", "type": "PassThrough", "id": "64ebc7d3-d5d7-41de-ab44-27e95437d3b7", "expectedControlType": "<PERSON><PERSON>", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "ScrollWheel", "type": "PassThrough", "id": "3d892bb4-0d60-43fa-abaf-896db5d4a58f", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDevicePosition", "type": "PassThrough", "id": "30887450-c850-41ca-b01f-f7459d213e87", "expectedControlType": "Vector3", "processors": "", "interactions": "", "initialStateCheck": false}, {"name": "TrackedDeviceOrientation", "type": "PassThrough", "id": "1f23abef-1156-4042-83a0-21685703277e", "expectedControlType": "Quaternion", "processors": "", "interactions": "", "initialStateCheck": false}], "bindings": [{"name": "Gamepad", "id": "1120c03f-ac44-4dc9-aae7-49a62cc4acf6", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "a25b01c0-005d-4f05-b40a-a9b2356b734b", "path": "<Gamepad>/leftStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "0219078b-3a22-41ea-be42-a8b72d834691", "path": "<Gamepad>/rightStick/up", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "d2faa2c1-eca7-4c70-9c35-04b2ade50219", "path": "<Gamepad>/leftStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "ce0bd8e5-4504-4a1c-8cf1-cbd8ee45c396", "path": "<Gamepad>/rightStick/down", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "07664088-3b34-433f-8478-98efeb2d5418", "path": "<Gamepad>/leftStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "dbbc0a0c-b01a-4e13-aeda-c07d00dab33b", "path": "<Gamepad>/rightStick/left", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "dad8a120-602a-4108-a0d8-86107fa0ea86", "path": "<Gamepad>/leftStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "adfb27b1-c50d-4475-86d4-50c21f3c5679", "path": "<Gamepad>/rightStick/right", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "3b996313-2e12-46a3-9761-945bdbabd91c", "path": "<Gamepad>/dpad", "interactions": "", "processors": "", "groups": ";Gamepad", "action": "Navigate", "isComposite": false, "isPartOfComposite": false}, {"name": "Joystick", "id": "aa4a7f5c-533c-4f4f-a45c-78f6fcb81350", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "66969257-5140-450b-b7d8-e96a0edf0e61", "path": "<Joystick>/stick/up", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "e6c70935-b0c8-4ebf-85eb-29e729a50de4", "path": "<Joystick>/stick/down", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "45831bec-1dc2-45a1-b726-90969668bc86", "path": "<Joystick>/stick/left", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "f1c62e44-d743-4a76-9238-f1876deff3d2", "path": "<Joystick>/stick/right", "interactions": "", "processors": "", "groups": "Joystick", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "Keyboard", "id": "02322927-cdc8-4921-a44f-5894bb9836df", "path": "2DVector", "interactions": "", "processors": "", "groups": "", "action": "Navigate", "isComposite": true, "isPartOfComposite": false}, {"name": "up", "id": "dcb46a85-0b30-4a44-afaf-6d6abb58b0ce", "path": "<Keyboard>/w", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "up", "id": "30b2b7c4-2423-4a2f-8628-1ad737ec36a7", "path": "<Keyboard>/upArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "f57620ec-98f5-4d79-97e2-306952d8e0ea", "path": "<Keyboard>/s", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "down", "id": "93a79243-d268-4111-98d0-b52240190100", "path": "<Keyboard>/downArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "d80ae1fb-3a08-4879-8758-ac7c875cdd4e", "path": "<Keyboard>/a", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "left", "id": "24a52019-72be-48b5-9d72-d29e7f3f545f", "path": "<Keyboard>/leftArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "69a3342a-ee9e-48cb-9a4c-90553086bea8", "path": "<Keyboard>/d", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "right", "id": "2360ce50-7d3d-4f72-9b11-ae1e76c1328f", "path": "<Keyboard>/rightArrow", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Navigate", "isComposite": false, "isPartOfComposite": true}, {"name": "", "id": "13010dc0-24de-4498-90f9-103134cfb0c1", "path": "*/{Submit}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Touch;Joystick;XR", "action": "Submit", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "b6512794-a660-4b29-ab43-da2ff9bf43d5", "path": "*/{Cancel}", "interactions": "", "processors": "", "groups": "Keyboard&Mouse;Gamepad;Touch;Joystick;XR", "action": "Cancel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "77d12eb6-ad2f-4eb5-b0a0-9b9bbe51eb91", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2ecd2e62-5542-45c7-8537-25a59df42858", "path": "<Pen>/position", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "290a4518-ecd8-45e7-ab66-d46401010e9e", "path": "<Touchscreen>/touch*/position", "interactions": "", "processors": "", "groups": "Touch", "action": "Point", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "69b19a6b-4204-45c2-8334-a839e1c08d39", "path": "<Mouse>/leftButton", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "e4573ca1-e3d9-4847-84e7-711a537853cd", "path": "<Pen>/tip", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "370b4801-2759-44fc-a57a-57f74646f782", "path": "<Touchscreen>/touch*/press", "interactions": "", "processors": "", "groups": "Touch", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9e2923e4-a243-4030-b691-08d77d803338", "path": "<XRController>/trigger", "interactions": "", "processors": "", "groups": "XR", "action": "Click", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "5ac6e3be-a806-4b8d-8495-cc78b25dbf7b", "path": "<Mouse>/scroll", "interactions": "", "processors": "", "groups": ";Keyboard&Mouse", "action": "ScrollWheel", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "9a356098-7167-486e-b695-98c6f3d7334e", "path": "<Mouse>/rightButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "RightClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "2d2a15a0-9397-4931-9a37-8d63b67fb76e", "path": "<Mouse>/middleButton", "interactions": "", "processors": "", "groups": "Keyboard&Mouse", "action": "MiddleClick", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "efb37256-5cf5-4dfa-bdce-e03733677a95", "path": "<XRController>/devicePosition", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDevicePosition", "isComposite": false, "isPartOfComposite": false}, {"name": "", "id": "6518b63a-f0d4-415d-8681-d87d9b743515", "path": "<XRController>/deviceRotation", "interactions": "", "processors": "", "groups": "XR", "action": "TrackedDeviceOrientation", "isComposite": false, "isPartOfComposite": false}]}, {"name": "Scene", "id": "1c394129-b1cb-42d6-aafd-6e136abd7065", "actions": [{"name": "Move", "type": "Value", "id": "c67e9a17-1d49-4a45-9830-f0d90a7d59b7", "expectedControlType": "Vector2", "processors": "", "interactions": "", "initialStateCheck": true}], "bindings": [{"name": "", "id": "5528cfc7-71b4-430a-b81f-e9c730233a4f", "path": "<Mouse>/position", "interactions": "", "processors": "", "groups": "", "action": "Move", "isComposite": false, "isPartOfComposite": false}]}], "controlSchemes": [{"name": "Keyboard&Mouse", "bindingGroup": "Keyboard&Mouse", "devices": [{"devicePath": "<Keyboard>", "isOptional": false, "isOR": false}, {"devicePath": "<Mouse>", "isOptional": false, "isOR": false}]}, {"name": "Gamepad", "bindingGroup": "Gamepad", "devices": [{"devicePath": "<Gamepad>", "isOptional": false, "isOR": false}]}, {"name": "Touch", "bindingGroup": "Touch", "devices": [{"devicePath": "<Touchscreen>", "isOptional": false, "isOR": false}]}, {"name": "Joystick", "bindingGroup": "Joystick", "devices": [{"devicePath": "<Joystick>", "isOptional": false, "isOR": false}]}, {"name": "XR", "bindingGroup": "XR", "devices": [{"devicePath": "<XRController>", "isOptional": false, "isOR": false}]}]}